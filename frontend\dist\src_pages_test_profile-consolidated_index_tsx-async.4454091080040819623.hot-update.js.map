{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.*******************.hot-update.js", "src/pages/test/profile-consolidated/UserProfileCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='14815717081795365802';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\r\n  Bar<PERSON>hartOutlined,\r\n  CheckOutlined,\r\n  EditOutlined,\r\n  LogoutOutlined,\r\n  MailOutlined,\r\n  PhoneOutlined,\r\n  SettingOutlined,\r\n  TagOutlined,\r\n  UserOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  Modal,\r\n  Row,\r\n  Space,\r\n  Steps,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  Avatar,\r\n} from \"antd\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { UserService } from \"@/services/user\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from \"@/types/api\";\r\nimport CustomDivider from \"@/components/CustomDivider\";\r\n\r\nconst { Title, Text } = Typography;\r\nconst { Step } = Steps;\r\n\r\nconst UserProfileCard: React.FC = () => {\r\n  // 用户详细信息状态\r\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\r\n    name: \"\",\r\n    position: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    telephone: \"\",\r\n    registerDate: \"\",\r\n    lastLoginTime: \"\",\r\n    lastLoginTeam: \"\",\r\n    teamCount: 0,\r\n    avatar: \"\",\r\n  });\r\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\r\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\r\n\r\n  // 个人统计数据状态\r\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\r\n    vehicles: 0,\r\n    personnel: 0,\r\n    warnings: 0,\r\n    alerts: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState<string | null>(null);\r\n\r\n  // 订阅计划数据\r\n  const subscriptionPlans = [\r\n    {\r\n      id: \"basic\",\r\n      name: \"基础版\",\r\n      price: 0,\r\n      description: \"适合小团队使用\",\r\n      features: [\"最多5个团队\", \"最多20辆车辆\", \"基础安全监控\", \"基本报告功能\"],\r\n    },\r\n    {\r\n      id: \"professional\",\r\n      name: \"专业版\",\r\n      price: 199,\r\n      description: \"适合中小型企业\",\r\n      features: [\r\n        \"最多20个团队\",\r\n        \"最多100辆车辆\",\r\n        \"高级安全监控\",\r\n        \"详细分析报告\",\r\n        \"设备状态预警\",\r\n        \"优先技术支持\",\r\n      ],\r\n    },\r\n    {\r\n      id: \"enterprise\",\r\n      name: \"企业版\",\r\n      price: 499,\r\n      description: \"适合大型企业\",\r\n      features: [\r\n        \"不限团队数量\",\r\n        \"不限车辆数量\",\r\n        \"AI安全分析\",\r\n        \"实时监控告警\",\r\n        \"定制化报告\",\r\n        \"专属客户经理\",\r\n        \"24/7技术支持\",\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // 当前订阅信息\r\n  const currentSubscription = {\r\n    planId: \"basic\",\r\n    expires: \"2025-12-31\",\r\n  };\r\n\r\n  // 状态管理\r\n  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);\r\n  const [subscriptionModalVisible, setSubscriptionModalVisible] =\r\n    useState(false);\r\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\r\n  const [logoutLoading, setLogoutLoading] = useState(false);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [editProfileForm] = Form.useForm();\r\n\r\n  const { setInitialState } = useModel('@@initialState');\r\n\r\n  // 获取用户数据\r\n  useEffect(() => {\r\n    console.log('UserProfileCard: useEffect 开始执行');\r\n\r\n    const fetchUserData = async () => {\r\n      try {\r\n        console.log('UserProfileCard: 开始获取用户数据');\r\n\r\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\r\n        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {\r\n          console.error('获取用户详细信息失败:', error);\r\n          setUserInfoError('获取用户详细信息失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const statsPromise = UserService.getUserPersonalStats().catch(error => {\r\n          console.error('获取统计数据失败:', error);\r\n          setStatsError('获取统计数据失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);\r\n\r\n        if (userDetail) {\r\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\r\n          setUserInfo(userDetail);\r\n          setUserInfoError(null);\r\n        }\r\n\r\n        if (stats) {\r\n          console.log('UserProfileCard: 获取到统计数据:', stats);\r\n          setPersonalStats(stats);\r\n          setStatsError(null);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('获取用户数据时发生未知错误:', error);\r\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\r\n        setStatsError('获取统计数据失败，请刷新页面重试');\r\n      } finally {\r\n        setUserInfoLoading(false);\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, []);\r\n\r\n  // 退出登录处理函数\r\n  const handleLogout = async () => {\r\n    try {\r\n      setLogoutLoading(true);\r\n\r\n      // 调用退出登录API\r\n      await AuthService.logout();\r\n\r\n      // 清除 initialState\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n\r\n      // 跳转到登录页面\r\n      history.push('/user/login');\r\n\r\n    } catch (error) {\r\n      console.error('退出登录失败:', error);\r\n      // 即使API调用失败，也要清除本地状态并跳转\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n      history.push('/user/login');\r\n    } finally {\r\n      setLogoutLoading(false);\r\n      setLogoutModalVisible(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className=\"dashboard-card\" styles={{ body: { padding: 32 } }}\r\n      >\r\n\r\n\r\n        {/* 用户信息主卡片 */}\r\n        {userInfoError ? (\r\n          <Alert\r\n            message=\"用户信息加载失败\"\r\n            description={userInfoError}\r\n            type=\"error\"\r\n            showIcon\r\n            style={{ marginBottom: 24 }}\r\n          />\r\n        ) : (\r\n          <Spin spinning={userInfoLoading}>\r\n            {/* 使用 Card 组件替代自定义 div */}\r\n            <Card\r\n              style={{\r\n                background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n                borderRadius: 16,\r\n                color: \"white\",\r\n                position: \"relative\",\r\n                overflow: \"hidden\",\r\n                height: 140,\r\n                border: \"none\",\r\n              }}\r\n              styles={{ body: { padding: 24, height: \"100%\" } }}\r\n            >\r\n              {/* 操作按钮区域 - 使用 Space 组件 */}\r\n              <Space\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: 16,\r\n                  right: 16,\r\n                  zIndex: 20,\r\n                }}\r\n                size={8}\r\n              >\r\n                <Tooltip title=\"设置\">\r\n                  <Dropdown\r\n                    menu={{\r\n                      items: [\r\n                        {\r\n                          key: \"editProfile\",\r\n                          icon: <EditOutlined />,\r\n                          label: \"修改资料\",\r\n                          onClick: () => {\r\n                            setEditProfileModalVisible(true);\r\n                            setCurrentStep(0);\r\n                            editProfileForm.setFieldsValue({\r\n                              name: userInfo.name,\r\n                              email: userInfo.email,\r\n                              telephone: userInfo.phone || userInfo.telephone,\r\n                            });\r\n                          },\r\n                        },\r\n                        {\r\n                          key: \"subscription\",\r\n                          icon: <TagOutlined />,\r\n                          label: \"订阅套餐\",\r\n                          onClick: () => setSubscriptionModalVisible(true),\r\n                        },\r\n                      ],\r\n                    }}\r\n                    trigger={[\"click\"]}\r\n                    placement=\"bottomRight\"\r\n                  >\r\n                    <Button\r\n                      type=\"text\"\r\n                      shape=\"circle\"\r\n                      icon={<SettingOutlined />}\r\n                      style={{\r\n                        color: \"rgba(255,255,255,0.9)\",\r\n                        backgroundColor: \"rgba(255,255,255,0.15)\",\r\n                        border: \"none\",\r\n                        transition: \"all 0.2s\",\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.25)\";\r\n                        e.currentTarget.style.color = \"white\";\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.15)\";\r\n                        e.currentTarget.style.color = \"rgba(255,255,255,0.9)\";\r\n                      }}\r\n                    />\r\n                  </Dropdown>\r\n                </Tooltip>\r\n\r\n                <Tooltip title=\"退出登录\">\r\n                  <Button\r\n                    type=\"text\"\r\n                    shape=\"circle\"\r\n                    icon={<LogoutOutlined />}\r\n                    onClick={() => setLogoutModalVisible(true)}\r\n                    style={{\r\n                      color: \"rgba(255,255,255,0.9)\",\r\n                      backgroundColor: \"rgba(255,255,255,0.15)\",\r\n                      border: \"none\",\r\n                      transition: \"all 0.2s\",\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = \"rgba(255,77,79,0.3)\";\r\n                      e.currentTarget.style.color = \"#ff4d4f\";\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.15)\";\r\n                      e.currentTarget.style.color = \"rgba(255,255,255,0.9)\";\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n              </Space>\r\n              {/* 装饰性背景元素 */}\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: -25,\r\n                  right: -25,\r\n                  width: 100,\r\n                  height: 100,\r\n                  background: \"rgba(255,255,255,0.1)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  bottom: -30,\r\n                  left: -30,\r\n                  width: 80,\r\n                  height: 80,\r\n                  background: \"rgba(255,255,255,0.05)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: \"50%\",\r\n                  right: \"20%\",\r\n                  width: 60,\r\n                  height: 60,\r\n                  background: \"rgba(255,255,255,0.03)\",\r\n                  borderRadius: \"50%\",\r\n                  transform: \"translateY(-50%)\",\r\n                }}\r\n              />\r\n\r\n              {/* 主要内容区域 - 使用 Flex 组件 */}\r\n              <Flex\r\n                align=\"center\"\r\n                gap={32}\r\n                style={{ position: \"relative\", zIndex: 1, width: \"100%\" }}\r\n              >\r\n                {/* 左侧：用户基本信息区域 */}\r\n                <Flex align=\"center\" style={{ flex: \"0 0 auto\" }}>\r\n                  {/* 用户头像 - 使用方形 Avatar 组件 */}\r\n                  <Avatar\r\n                    size={64}\r\n                    shape=\"square\"\r\n                    src={userInfo.avatar}\r\n                    style={{\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      marginRight: 20,\r\n                      fontSize: 24,\r\n                      fontWeight: 600,\r\n                    }}\r\n                  >\r\n                    {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : <UserOutlined />}\r\n                  </Avatar>\r\n\r\n                  {/* 用户信息 - 使用 Space 组件垂直布局 */}\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Title\r\n                      level={3}\r\n                      style={{\r\n                        margin: 0,\r\n                        color: \"white\",\r\n                        fontSize: 22,\r\n                        fontWeight: 600,\r\n                      }}\r\n                    >\r\n                      {userInfo.name || \"加载中...\"}\r\n                    </Title>\r\n\r\n                    {/* 联系信息 - 使用 Space 组件垂直排列 */}\r\n                    <Space direction=\"vertical\" size={4}>\r\n                      {userInfo.email && (\r\n                        <Space size={6} align=\"center\">\r\n                          <MailOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                          <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                            {userInfo.email}\r\n                          </Text>\r\n                        </Space>\r\n                      )}\r\n                      {(userInfo.phone || userInfo.telephone) && (\r\n                        <Space size={6} align=\"center\">\r\n                          <PhoneOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                          <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                            {userInfo.phone || userInfo.telephone}\r\n                          </Text>\r\n                        </Space>\r\n                      )}\r\n                    </Space>\r\n\r\n                    {/* 注册日期 */}\r\n                    {userInfo.registerDate && (\r\n                      <Text style={{ fontSize: 13, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                        注册于 {userInfo.registerDate}\r\n                      </Text>\r\n                    )}\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 中间：数据统计概览 - 使用 Flex 组件 */}\r\n                <CustomDivider\r\n                  orientation=\"vertical\"\r\n                  color=\"rgba(255,255,255,0.2)\"\r\n                  style={{ height: '80%', alignSelf: 'center' }}\r\n                />\r\n                <Flex\r\n                  vertical\r\n                  style={{\r\n                    flex: \"1 1 auto\",\r\n                    minWidth: 0,\r\n                    paddingLeft: 20,\r\n                  }}\r\n                >\r\n                  {/* 标题区域 - 使用 Space 组件 */}\r\n                  <Space\r\n                    align=\"center\"\r\n                    style={{\r\n                      justifyContent: \"center\",\r\n                      marginBottom: 30,\r\n                      height: 20,\r\n                    }}\r\n                  >\r\n                    <BarChartOutlined\r\n                      style={{\r\n                        fontSize: 16,\r\n                        color: \"rgba(255,255,255,0.9)\",\r\n                      }}\r\n                    />\r\n                    <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 14, fontWeight: 600, lineHeight: 1 }}>\r\n                      数据概览\r\n                    </Text>\r\n                  </Space>\r\n\r\n                  {statsError ? (\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\" }}>\r\n                      数据加载失败\r\n                    </Text>\r\n                  ) : (\r\n                    <Spin spinning={statsLoading}>\r\n                      {/* 统计数据 - 使用 Flex 组件和自定义样式 */}\r\n                      <Flex justify=\"space-around\" align=\"center\">\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.vehicles}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            车辆\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.personnel}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            人员\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.warnings}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            预警\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.alerts}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            告警\r\n                          </div>\r\n                        </div>\r\n                      </Flex>\r\n                    </Spin>\r\n                  )}\r\n                </Flex>\r\n\r\n                {/* 右侧：最近活动信息 - 使用 Space 组件 */}\r\n                <CustomDivider\r\n                  orientation=\"vertical\"\r\n                  color=\"rgba(255,255,255,0.2)\"\r\n                  style={{ height: '80%', alignSelf: 'center' }}\r\n                />\r\n                <Space\r\n                  direction=\"vertical\"\r\n                  size={10}\r\n                  style={{\r\n                    flex: \"0 0 auto\",\r\n                    paddingLeft: 20,\r\n                    width: 240,\r\n                  }}\r\n                >\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                      最后登录时间\r\n                    </Text>\r\n                    <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                      {userInfo.lastLoginTime || \"暂无记录\"}\r\n                    </Text>\r\n                  </Space>\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                      最后登录团队\r\n                    </Text>\r\n                    <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                      {userInfo.lastLoginTeam || \"暂无记录\"}\r\n                    </Text>\r\n                  </Space>\r\n                </Space>\r\n              </Flex>\r\n            </Card>\r\n          </Spin>\r\n        )}\r\n      </Card>\r\n\r\n      {/* 修改资料模态框 */}\r\n      <Modal\r\n        title=\"修改个人资料\"\r\n        open={editProfileModalVisible}\r\n        onCancel={() => {\r\n          setEditProfileModalVisible(false);\r\n          setCurrentStep(0);\r\n        }}\r\n        footer={[\r\n          currentStep === 1 && (\r\n            <Button key=\"back\" onClick={() => setCurrentStep(0)}>\r\n              上一步\r\n            </Button>\r\n          ),\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              if (currentStep === 0) {\r\n                editProfileForm.validateFields().then(() => {\r\n                  setCurrentStep(1);\r\n                });\r\n              } else {\r\n                editProfileForm.validateFields().then((values) => {\r\n                  console.log(\"个人资料表单值:\", values);\r\n                  // 提交表单，这里简化处理，只输出到控制台\r\n                  setEditProfileModalVisible(false);\r\n                  setCurrentStep(0);\r\n                });\r\n              }\r\n            }}\r\n          >\r\n            {currentStep === 0 ? \"下一步\" : \"确定\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Steps current={currentStep} style={{ marginBottom: 16 }}>\r\n          <Step title=\"填写信息\" />\r\n          <Step title=\"安全验证\" />\r\n        </Steps>\r\n\r\n        <Form form={editProfileForm} layout=\"vertical\" requiredMark={false}>\r\n          {currentStep === 0 ? (\r\n            <>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"用户名\"\r\n                rules={[{ required: true, message: \"请输入用户名\" }]}\r\n              >\r\n                <Input placeholder=\"请输入用户名\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"邮箱\"\r\n                rules={[\r\n                  { required: true, message: \"请输入邮箱地址\" },\r\n                  { type: \"email\", message: \"请输入有效的邮箱地址\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入邮箱地址\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"telephone\"\r\n                label=\"手机号\"\r\n                rules={[\r\n                  { required: true, message: \"请输入手机号\" },\r\n                  { pattern: /^1\\d{10}$/, message: \"请输入有效的手机号\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入手机号\" />\r\n              </Form.Item>\r\n            </>\r\n          ) : (\r\n            /* 验证码步骤 - 使用 Space 组件 */\r\n            <Space direction=\"vertical\" align=\"center\" style={{ width: \"100%\" }}>\r\n              <Text style={{ margin: \"12px 0\" }}>\r\n                验证码已发送至您的手机号{\" \"}\r\n                <Text strong>{editProfileForm.getFieldValue(\"telephone\")}</Text>\r\n              </Text>\r\n              <Form.Item\r\n                name=\"verificationCode\"\r\n                label=\"验证码\"\r\n                rules={[{ required: true, message: \"请输入验证码\" }]}\r\n                style={{ textAlign: \"center\" }}\r\n              >\r\n                <Input\r\n                  placeholder=\"请输入6位验证码\"\r\n                  maxLength={6}\r\n                  style={{ width: \"50%\", textAlign: \"center\" }}\r\n                />\r\n              </Form.Item>\r\n              <Button type=\"link\" style={{ padding: 0 }}>\r\n                重新发送验证码\r\n              </Button>\r\n            </Space>\r\n          )}\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 订阅套餐模态框 */}\r\n      <Modal\r\n        title=\"订阅套餐\"\r\n        open={subscriptionModalVisible}\r\n        onCancel={() => setSubscriptionModalVisible(false)}\r\n        footer={null}\r\n        width={800}\r\n      >\r\n        {/* 当前套餐信息 - 使用 Card 组件 */}\r\n        <Card\r\n          size=\"small\"\r\n          style={{\r\n            background: \"#f9f9f9\",\r\n            marginBottom: 16,\r\n          }}\r\n        >\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Text strong>当前套餐: </Text>\r\n            <Tag color=\"green\" style={{ marginLeft: 8, fontSize: 13 }}>\r\n              {\r\n                subscriptionPlans.find(\r\n                  (p) => p.id === currentSubscription.planId\r\n                )?.name\r\n              }\r\n            </Tag>\r\n            <Text type=\"secondary\">\r\n              到期时间: {currentSubscription.expires}\r\n            </Text>\r\n          </Flex>\r\n        </Card>\r\n\r\n        <Row gutter={24}>\r\n          {subscriptionPlans.map((plan) => (\r\n            <Col span={8} key={plan.id}>\r\n              {/* 套餐卡片 - 使用 Card 组件 */}\r\n              <Card\r\n                style={{\r\n                  height: \"100%\",\r\n                  border: `1px solid ${\r\n                    plan.id === currentSubscription.planId\r\n                      ? \"#52c41a\"\r\n                      : \"#d9d9d9\"\r\n                  }`,\r\n                  position: \"relative\",\r\n                }}\r\n                styles={{ body: { padding: 16 } }}\r\n              >\r\n                {plan.id === currentSubscription.planId && (\r\n                  <Tag\r\n                    color=\"green\"\r\n                    style={{\r\n                      position: \"absolute\",\r\n                      top: -10,\r\n                      right: -10,\r\n                      borderRadius: 2,\r\n                      boxShadow: \"0 2px 8px rgba(0,0,0,0.1)\",\r\n                    }}\r\n                  >\r\n                    当前套餐\r\n                  </Tag>\r\n                )}\r\n                <Title\r\n                  level={4}\r\n                  style={{ textAlign: \"center\", margin: \"12px 0 8px\" }}\r\n                >\r\n                  {plan.name}\r\n                </Title>\r\n                <Flex vertical align=\"center\" style={{ marginBottom: 12 }}>\r\n                  {plan.price > 0 ? (\r\n                    <>\r\n                      <Title level={2} style={{ marginBottom: 0 }}>\r\n                        ¥{plan.price}\r\n                      </Title>\r\n                      <Text type=\"secondary\">/月</Text>\r\n                    </>\r\n                  ) : (\r\n                    <Title\r\n                      level={2}\r\n                      style={{ color: \"#52c41a\", marginBottom: 0 }}\r\n                    >\r\n                      免费\r\n                    </Title>\r\n                  )}\r\n                  <Text type=\"secondary\" style={{ marginTop: 4 }}>\r\n                    {plan.description}\r\n                  </Text>\r\n                </Flex>\r\n\r\n                <CustomDivider color=\"#f0f0f0\" margin=\"8px 0\" />\r\n\r\n                {/* 功能列表 - 使用 Space 组件 */}\r\n                <Space direction=\"vertical\" size={6} style={{ minHeight: 170, width: \"100%\" }}>\r\n                  {plan.features.map((feature, index) => (\r\n                    <Space key={index} align=\"start\">\r\n                      <CheckOutlined\r\n                        style={{\r\n                          color: \"#52c41a\",\r\n                          marginTop: 4,\r\n                        }}\r\n                      />\r\n                      <Text>{feature}</Text>\r\n                    </Space>\r\n                  ))}\r\n                </Space>\r\n\r\n                {plan.id !== currentSubscription.planId ? (\r\n                  <Button\r\n                    type=\"primary\"\r\n                    block\r\n                    style={{\r\n                      marginTop: 12,\r\n                      boxShadow: \"0 2px 8px rgba(24, 144, 255, 0.3)\",\r\n                    }}\r\n                    onClick={() => {\r\n                      console.log(\"选择套餐:\", plan);\r\n                      setSubscriptionModalVisible(false);\r\n                    }}\r\n                  >\r\n                    立即订阅\r\n                  </Button>\r\n                ) : (\r\n                  <Button\r\n                    block\r\n                    style={{\r\n                      marginTop: 12,\r\n                      background: \"#f6ffed\",\r\n                      borderColor: \"#b7eb8f\",\r\n                      color: \"#389e0d\",\r\n                    }}\r\n                    disabled\r\n                  >\r\n                    当前套餐\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            </Col>\r\n          ))}\r\n        </Row>\r\n\r\n        <Flex justify=\"center\" style={{ marginTop: 20 }}>\r\n          <Text type=\"secondary\">订阅服务自动续费，可随时取消</Text>\r\n        </Flex>\r\n      </Modal>\r\n\r\n      {/* 退出登录确认模态框 */}\r\n      <Modal\r\n        title=\"确认退出登录\"\r\n        open={logoutModalVisible}\r\n        onCancel={() => setLogoutModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setLogoutModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"confirm\"\r\n            type=\"primary\"\r\n            danger\r\n            loading={logoutLoading}\r\n            onClick={handleLogout}\r\n          >\r\n            确认退出\r\n          </Button>,\r\n        ]}\r\n        width={400}\r\n      >\r\n        {/* 退出登录确认内容 - 使用 Space 组件 */}\r\n        <Space direction=\"vertical\" align=\"center\" style={{ width: \"100%\", padding: \"20px 0\" }}>\r\n          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />\r\n          <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>\r\n          <Text type=\"secondary\" style={{ textAlign: \"center\" }}>\r\n            退出后您需要重新登录才能继续使用系统\r\n          </Text>\r\n        </Space>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UserProfileCard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCm1Bb;;;2BAAA;;;;;;;0CA50BO;yCAmBA;oFACoC;yCACf;6CACA;wCACM;2FAER;;;;;;;;;;YAE1B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAK;YAEtB,MAAM,kBAA4B;oBAgpBlB;;gBA/oBd,WAAW;gBACX,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,WAAW;gBACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,MAAM,oBAAoB;oBACxB;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BAAC;4BAAU;4BAAW;4BAAU;yBAAS;oBACrD;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;iBACD;gBAED,SAAS;gBACT,MAAM,sBAAsB;oBAC1B,QAAQ;oBACR,SAAS;gBACX;gBAEA,OAAO;gBACP,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,IAAA,eAAQ,EAAC;gBACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,IAAA,eAAQ,EAAC;gBACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;gBACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,gBAAgB,GAAG,UAAI,CAAC,OAAO;gBAEtC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,QAAQ,GAAG,CAAC;oBAEZ,MAAM,gBAAgB;wBACpB,IAAI;4BACF,QAAQ,GAAG,CAAC;4BAEZ,8BAA8B;4BAC9B,MAAM,oBAAoB,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;gCACjE,QAAQ,KAAK,CAAC,eAAe;gCAC7B,iBAAiB;gCACjB,OAAO;4BACT;4BAEA,MAAM,eAAe,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;gCAC5D,QAAQ,KAAK,CAAC,aAAa;gCAC3B,cAAc;gCACd,OAAO;4BACT;4BAEA,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAAC;gCAAmB;6BAAa;4BAE/E,IAAI,YAAY;gCACd,QAAQ,GAAG,CAAC,+BAA+B;gCAC3C,YAAY;gCACZ,iBAAiB;4BACnB;4BAEA,IAAI,OAAO;gCACT,QAAQ,GAAG,CAAC,6BAA6B;gCACzC,iBAAiB;gCACjB,cAAc;4BAChB;wBAEF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kBAAkB;4BAChC,iBAAiB;4BACjB,cAAc;wBAChB,SAAU;4BACR,mBAAmB;4BACnB,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,WAAW;gBACX,MAAM,eAAe;oBACnB,IAAI;wBACF,iBAAiB;wBAEjB,YAAY;wBACZ,MAAM,qBAAW,CAAC,MAAM;wBAExB,kBAAkB;wBAClB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAGF,UAAU;wBACV,YAAO,CAAC,IAAI,CAAC;oBAEf,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,wBAAwB;wBACxB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAEF,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,iBAAiB;wBACjB,sBAAsB;oBACxB;gBACF;gBAEA,qBACE;;sCACE,2BAAC,UAAI;4BACH,WAAU;4BAAiB,QAAQ;gCAAE,MAAM;oCAAE,SAAS;gCAAG;4BAAE;sCAK1D,8BACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,cAAc;gCAAG;;;;;qDAG5B,2BAAC,UAAI;gCAAC,UAAU;0CAEd,cAAA,2BAAC,UAAI;oCACH,OAAO;wCACL,YAAY;wCACZ,cAAc;wCACd,OAAO;wCACP,UAAU;wCACV,UAAU;wCACV,QAAQ;wCACR,QAAQ;oCACV;oCACA,QAAQ;wCAAE,MAAM;4CAAE,SAAS;4CAAI,QAAQ;wCAAO;oCAAE;;sDAGhD,2BAAC,WAAK;4CACJ,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,QAAQ;4CACV;4CACA,MAAM;;8DAEN,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,cAAQ;wDACP,MAAM;4DACJ,OAAO;gEACL;oEACE,KAAK;oEACL,oBAAM,2BAAC,mBAAY;;;;;oEACnB,OAAO;oEACP,SAAS;wEACP,2BAA2B;wEAC3B,eAAe;wEACf,gBAAgB,cAAc,CAAC;4EAC7B,MAAM,SAAS,IAAI;4EACnB,OAAO,SAAS,KAAK;4EACrB,WAAW,SAAS,KAAK,IAAI,SAAS,SAAS;wEACjD;oEACF;gEACF;gEACA;oEACE,KAAK;oEACL,oBAAM,2BAAC,kBAAW;;;;;oEAClB,OAAO;oEACP,SAAS,IAAM,4BAA4B;gEAC7C;6DACD;wDACH;wDACA,SAAS;4DAAC;yDAAQ;wDAClB,WAAU;kEAEV,cAAA,2BAAC,YAAM;4DACL,MAAK;4DACL,OAAM;4DACN,oBAAM,2BAAC,sBAAe;;;;;4DACtB,OAAO;gEACL,OAAO;gEACP,iBAAiB;gEACjB,QAAQ;gEACR,YAAY;4DACd;4DACA,cAAc,CAAC;gEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4DAChC;4DACA,cAAc,CAAC;gEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4DAChC;;;;;;;;;;;;;;;;8DAKN,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,YAAM;wDACL,MAAK;wDACL,OAAM;wDACN,oBAAM,2BAAC,qBAAc;;;;;wDACrB,SAAS,IAAM,sBAAsB;wDACrC,OAAO;4DACL,OAAO;4DACP,iBAAiB;4DACjB,QAAQ;4DACR,YAAY;wDACd;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4DACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAChC;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4DACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAChC;;;;;;;;;;;;;;;;;sDAKN,2BAAC;4CACC,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,cAAc;4CAChB;;;;;;sDAEF,2BAAC;4CACC,OAAO;gDACL,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,cAAc;4CAChB;;;;;;sDAEF,2BAAC;4CACC,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,cAAc;gDACd,WAAW;4CACb;;;;;;sDAIF,2BAAC,UAAI;4CACH,OAAM;4CACN,KAAK;4CACL,OAAO;gDAAE,UAAU;gDAAY,QAAQ;gDAAG,OAAO;4CAAO;;8DAGxD,2BAAC,UAAI;oDAAC,OAAM;oDAAS,OAAO;wDAAE,MAAM;oDAAW;;sEAE7C,2BAAC,YAAM;4DACL,MAAM;4DACN,OAAM;4DACN,KAAK,SAAS,MAAM;4DACpB,OAAO;gEACL,iBAAiB;gEACjB,aAAa;gEACb,UAAU;gEACV,YAAY;4DACd;sEAEC,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAAK,2BAAC,mBAAY;;;;;;;;;;sEAIxE,2BAAC,WAAK;4DAAC,WAAU;4DAAW,MAAM;;8EAChC,2BAAC;oEACC,OAAO;oEACP,OAAO;wEACL,QAAQ;wEACR,OAAO;wEACP,UAAU;wEACV,YAAY;oEACd;8EAEC,SAAS,IAAI,IAAI;;;;;;8EAIpB,2BAAC,WAAK;oEAAC,WAAU;oEAAW,MAAM;;wEAC/B,SAAS,KAAK,kBACb,2BAAC,WAAK;4EAAC,MAAM;4EAAG,OAAM;;8FACpB,2BAAC,mBAAY;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAwB;;;;;;8FACpE,2BAAC;oFAAK,OAAO;wFAAE,OAAO;wFAAyB,UAAU;oFAAG;8FACzD,SAAS,KAAK;;;;;;;;;;;;wEAInB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,mBACnC,2BAAC,WAAK;4EAAC,MAAM;4EAAG,OAAM;;8FACpB,2BAAC,oBAAa;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAwB;;;;;;8FACrE,2BAAC;oFAAK,OAAO;wFAAE,OAAO;wFAAyB,UAAU;oFAAG;8FACzD,SAAS,KAAK,IAAI,SAAS,SAAS;;;;;;;;;;;;;;;;;;gEAO5C,SAAS,YAAY,kBACpB,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAyB,YAAY;oEAAI;;wEAAG;wEACzE,SAAS,YAAY;;;;;;;;;;;;;;;;;;;8DAOlC,2BAAC,sBAAa;oDACZ,aAAY;oDACZ,OAAM;oDACN,OAAO;wDAAE,QAAQ;wDAAO,WAAW;oDAAS;;;;;;8DAE9C,2BAAC,UAAI;oDACH,QAAQ;oDACR,OAAO;wDACL,MAAM;wDACN,UAAU;wDACV,aAAa;oDACf;;sEAGA,2BAAC,WAAK;4DACJ,OAAM;4DACN,OAAO;gEACL,gBAAgB;gEAChB,cAAc;gEACd,QAAQ;4DACV;;8EAEA,2BAAC,uBAAgB;oEACf,OAAO;wEACL,UAAU;wEACV,OAAO;oEACT;;;;;;8EAEF,2BAAC;oEAAK,OAAO;wEAAE,OAAO;wEAAyB,UAAU;wEAAI,YAAY;wEAAK,YAAY;oEAAE;8EAAG;;;;;;;;;;;;wDAKhG,2BACC,2BAAC;4DAAK,OAAO;gEAAE,UAAU;gEAAI,OAAO;4DAAwB;sEAAG;;;;;iFAI/D,2BAAC,UAAI;4DAAC,UAAU;sEAEd,cAAA,2BAAC,UAAI;gEAAC,SAAQ;gEAAe,OAAM;;kFACjC,2BAAC;wEAAI,OAAO;4EAAE,WAAW;wEAAS;;0FAChC,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,YAAY;oFACZ,OAAO;oFACP,YAAY;gFACd;0FACG,cAAc,QAAQ;;;;;;0FAEzB,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,OAAO;oFACP,WAAW;gFACb;0FAAG;;;;;;;;;;;;kFAIL,2BAAC;wEAAI,OAAO;4EAAE,WAAW;wEAAS;;0FAChC,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,YAAY;oFACZ,OAAO;oFACP,YAAY;gFACd;0FACG,cAAc,SAAS;;;;;;0FAE1B,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,OAAO;oFACP,WAAW;gFACb;0FAAG;;;;;;;;;;;;kFAIL,2BAAC;wEAAI,OAAO;4EAAE,WAAW;wEAAS;;0FAChC,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,YAAY;oFACZ,OAAO;oFACP,YAAY;gFACd;0FACG,cAAc,QAAQ;;;;;;0FAEzB,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,OAAO;oFACP,WAAW;gFACb;0FAAG;;;;;;;;;;;;kFAIL,2BAAC;wEAAI,OAAO;4EAAE,WAAW;wEAAS;;0FAChC,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,YAAY;oFACZ,OAAO;oFACP,YAAY;gFACd;0FACG,cAAc,MAAM;;;;;;0FAEvB,2BAAC;gFAAI,OAAO;oFACV,UAAU;oFACV,OAAO;oFACP,WAAW;gFACb;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAUb,2BAAC,sBAAa;oDACZ,aAAY;oDACZ,OAAM;oDACN,OAAO;wDAAE,QAAQ;wDAAO,WAAW;oDAAS;;;;;;8DAE9C,2BAAC,WAAK;oDACJ,WAAU;oDACV,MAAM;oDACN,OAAO;wDACL,MAAM;wDACN,aAAa;wDACb,OAAO;oDACT;;sEAEA,2BAAC,WAAK;4DAAC,WAAU;4DAAW,MAAM;;8EAChC,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAyB,YAAY;oEAAI;8EAAG;;;;;;8EAGhF,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAS,YAAY;wEAAK,YAAY;oEAAI;8EAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;sEAG/B,2BAAC,WAAK;4DAAC,WAAU;4DAAW,MAAM;;8EAChC,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAyB,YAAY;oEAAI;8EAAG;;;;;;8EAGhF,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAS,YAAY;wEAAK,YAAY;oEAAI;8EAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW3C,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,2BAA2B;gCAC3B,eAAe;4BACjB;4BACA,QAAQ;gCACN,gBAAgB,mBACd,2BAAC,YAAM;oCAAY,SAAS,IAAM,eAAe;8CAAI;mCAAzC;;;;;8CAId,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;wCACP,IAAI,gBAAgB,GAClB,gBAAgB,cAAc,GAAG,IAAI,CAAC;4CACpC,eAAe;wCACjB;6CAEA,gBAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;4CACrC,QAAQ,GAAG,CAAC,YAAY;4CACxB,sBAAsB;4CACtB,2BAA2B;4CAC3B,eAAe;wCACjB;oCAEJ;8CAEC,gBAAgB,IAAI,QAAQ;mCAjBzB;;;;;6BAmBP;;8CAED,2BAAC,WAAK;oCAAC,SAAS;oCAAa,OAAO;wCAAE,cAAc;oCAAG;;sDACrD,2BAAC;4CAAK,OAAM;;;;;;sDACZ,2BAAC;4CAAK,OAAM;;;;;;;;;;;;8CAGd,2BAAC,UAAI;oCAAC,MAAM;oCAAiB,QAAO;oCAAW,cAAc;8CAC1D,gBAAgB,kBACf;;0DACE,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;0DAE9C,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAU;oDACrC;wDAAE,MAAM;wDAAS,SAAS;oDAAa;iDACxC;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAS;oDACpC;wDAAE,SAAS;wDAAa,SAAS;oDAAY;iDAC9C;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;;uDAIvB,uBAAuB,iBACvB,2BAAC,WAAK;wCAAC,WAAU;wCAAW,OAAM;wCAAS,OAAO;4CAAE,OAAO;wCAAO;;0DAChE,2BAAC;gDAAK,OAAO;oDAAE,QAAQ;gDAAS;;oDAAG;oDACpB;kEACb,2BAAC;wDAAK,MAAM;kEAAE,gBAAgB,aAAa,CAAC;;;;;;;;;;;;0DAE9C,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;gDAC9C,OAAO;oDAAE,WAAW;gDAAS;0DAE7B,cAAA,2BAAC,WAAK;oDACJ,aAAY;oDACZ,WAAW;oDACX,OAAO;wDAAE,OAAO;wDAAO,WAAW;oDAAS;;;;;;;;;;;0DAG/C,2BAAC,YAAM;gDAAC,MAAK;gDAAO,OAAO;oDAAE,SAAS;gDAAE;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCASnD,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,4BAA4B;4BAC5C,QAAQ;4BACR,OAAO;;8CAGP,2BAAC,UAAI;oCACH,MAAK;oCACL,OAAO;wCACL,YAAY;wCACZ,cAAc;oCAChB;8CAEA,cAAA,2BAAC,UAAI;wCAAC,SAAQ;wCAAgB,OAAM;;0DAClC,2BAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,2BAAC,SAAG;gDAAC,OAAM;gDAAQ,OAAO;oDAAE,YAAY;oDAAG,UAAU;gDAAG;2DAEpD,0BAAA,kBAAkB,IAAI,CACpB,CAAC,IAAM,EAAE,EAAE,KAAK,oBAAoB,MAAM,eAD5C,8CAAA,wBAEG,IAAI;;;;;;0DAGX,2BAAC;gDAAK,MAAK;;oDAAY;oDACd,oBAAoB,OAAO;;;;;;;;;;;;;;;;;;8CAKxC,2BAAC,SAAG;oCAAC,QAAQ;8CACV,kBAAkB,GAAG,CAAC,CAAC,qBACtB,2BAAC,SAAG;4CAAC,MAAM;sDAET,cAAA,2BAAC,UAAI;gDACH,OAAO;oDACL,QAAQ;oDACR,QAAQ,CAAC,UAAU,EACjB,KAAK,EAAE,KAAK,oBAAoB,MAAM,GAClC,YACA,UACL,CAAC;oDACF,UAAU;gDACZ;gDACA,QAAQ;oDAAE,MAAM;wDAAE,SAAS;oDAAG;gDAAE;;oDAE/B,KAAK,EAAE,KAAK,oBAAoB,MAAM,kBACrC,2BAAC,SAAG;wDACF,OAAM;wDACN,OAAO;4DACL,UAAU;4DACV,KAAK;4DACL,OAAO;4DACP,cAAc;4DACd,WAAW;wDACb;kEACD;;;;;;kEAIH,2BAAC;wDACC,OAAO;wDACP,OAAO;4DAAE,WAAW;4DAAU,QAAQ;wDAAa;kEAElD,KAAK,IAAI;;;;;;kEAEZ,2BAAC,UAAI;wDAAC,QAAQ;wDAAC,OAAM;wDAAS,OAAO;4DAAE,cAAc;wDAAG;;4DACrD,KAAK,KAAK,GAAG,kBACZ;;kFACE,2BAAC;wEAAM,OAAO;wEAAG,OAAO;4EAAE,cAAc;wEAAE;;4EAAG;4EACzC,KAAK,KAAK;;;;;;;kFAEd,2BAAC;wEAAK,MAAK;kFAAY;;;;;;;6FAGzB,2BAAC;gEACC,OAAO;gEACP,OAAO;oEAAE,OAAO;oEAAW,cAAc;gEAAE;0EAC5C;;;;;;0EAIH,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,WAAW;gEAAE;0EAC1C,KAAK,WAAW;;;;;;;;;;;;kEAIrB,2BAAC,sBAAa;wDAAC,OAAM;wDAAU,QAAO;;;;;;kEAGtC,2BAAC,WAAK;wDAAC,WAAU;wDAAW,MAAM;wDAAG,OAAO;4DAAE,WAAW;4DAAK,OAAO;wDAAO;kEACzE,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,2BAAC,WAAK;gEAAa,OAAM;;kFACvB,2BAAC,oBAAa;wEACZ,OAAO;4EACL,OAAO;4EACP,WAAW;wEACb;;;;;;kFAEF,2BAAC;kFAAM;;;;;;;+DAPG;;;;;;;;;;oDAYf,KAAK,EAAE,KAAK,oBAAoB,MAAM,iBACrC,2BAAC,YAAM;wDACL,MAAK;wDACL,KAAK;wDACL,OAAO;4DACL,WAAW;4DACX,WAAW;wDACb;wDACA,SAAS;4DACP,QAAQ,GAAG,CAAC,SAAS;4DACrB,4BAA4B;wDAC9B;kEACD;;;;;6EAID,2BAAC,YAAM;wDACL,KAAK;wDACL,OAAO;4DACL,WAAW;4DACX,YAAY;4DACZ,aAAa;4DACb,OAAO;wDACT;wDACA,QAAQ;kEACT;;;;;;;;;;;;2CAjGY,KAAK,EAAE;;;;;;;;;;8CA0G9B,2BAAC,UAAI;oCAAC,SAAQ;oCAAS,OAAO;wCAAE,WAAW;oCAAG;8CAC5C,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;sCAK3B,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,sBAAsB;4BACtC,QAAQ;8CACN,2BAAC,YAAM;oCAAc,SAAS,IAAM,sBAAsB;8CAAQ;mCAAtD;;;;;8CAGZ,2BAAC,YAAM;oCAEL,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,SAAS;8CACV;mCALK;;;;;6BAQP;4BACD,OAAO;sCAGP,cAAA,2BAAC,WAAK;gCAAC,WAAU;gCAAW,OAAM;gCAAS,OAAO;oCAAE,OAAO;oCAAQ,SAAS;gCAAS;;kDACnF,2BAAC,qBAAc;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDACxD,2BAAC;wCAAK,MAAM;wCAAC,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;kDACtC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,WAAW;wCAAS;kDAAG;;;;;;;;;;;;;;;;;;;YAOjE;eA5yBM;;oBAgFsB,UAAI,CAAC;oBAEH,aAAQ;;;iBAlFhC;gBA8yBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDn1BD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}